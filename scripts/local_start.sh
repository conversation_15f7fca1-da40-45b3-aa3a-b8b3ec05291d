#!/bin/bash

# Скрипт для локального запуска бота (без Docker)

echo "🚀 Локальный запуск телеграм бота..."

# Проверяем наличие .env.dev файла
if [ ! -f ".env.dev" ]; then
    echo "❌ Файл .env.dev не найден!"
    echo "📝 Создайте файл .env.dev на основе .env.example"
    echo "cp .env.example .env.dev"
    echo "nano .env.dev"
    exit 1
fi

# Проверяем наличие BOT_TOKEN
if ! grep -q "BOT_TOKEN=.*[^[:space:]]" .env.dev || grep -q "BOT_TOKEN=your_bot_token_here" .env.dev; then
    echo "❌ BOT_TOKEN не настроен в .env.dev!"
    echo "📝 Отредактируйте файл .env.dev и добавьте реальный токен бота"
    exit 1
fi

echo "✅ Конфигурация найдена"

# Проверяем Python зависимости
echo "📦 Проверка зависимостей..."
if ! python -c "import aiogram" 2>/dev/null; then
    echo "❌ Зависимости не установлены!"
    echo "📦 Установите зависимости: pip install -r requirements.txt"
    exit 1
fi

# Запускаем только Redis и PostgreSQL в Docker
echo "🐳 Запуск Redis и PostgreSQL в Docker..."
docker-compose -f docker-compose.dev.yml --env-file .env.dev up -d postgres redis

# Ждем готовности сервисов
echo "⏳ Ожидание готовности сервисов..."
sleep 5

# Проверяем подключение к Redis
echo "🔍 Проверка Redis..."
if python scripts/test_redis.py; then
    echo "✅ Redis готов"
else
    echo "⚠️ Redis недоступен, будет использоваться MemoryStorage"
fi

# Запускаем бота локально
echo "🤖 Запуск бота локально..."
export $(cat .env.dev | grep -v '^#' | xargs)
export REDIS_HOST=localhost  # Переопределяем для локального запуска
python main.py
