#!/bin/bash

# Скрипт для запуска бота в режиме разработки

echo "🚀 Запуск телеграм бота в режиме разработки..."

# Проверяем наличие .env.dev файла
if [ ! -f ".env.dev" ]; then
    echo "❌ Файл .env.dev не найден!"
    echo "📝 Создайте файл .env.dev на основе .env.example"
    echo "cp .env.example .env.dev"
    echo "nano .env.dev"
    exit 1
fi

# Проверяем наличие BOT_TOKEN
if ! grep -q "BOT_TOKEN=.*[^[:space:]]" .env.dev || grep -q "BOT_TOKEN=your_bot_token_here" .env.dev; then
    echo "❌ BOT_TOKEN не настроен в .env.dev!"
    echo "📝 Отредактируйте файл .env.dev и добавьте реальный токен бота"
    exit 1
fi

echo "✅ Конфигурация найдена"

# Останавливаем существующие контейнеры
echo "🛑 Остановка существующих контейнеров..."
docker-compose -f docker-compose.dev.yml --env-file .env.dev down

# Собираем образ
echo "🔨 Сборка образа..."
docker-compose -f docker-compose.dev.yml --env-file .env.dev build

# Запускаем сервисы
echo "🚀 Запуск сервисов..."
docker-compose -f docker-compose.dev.yml --env-file .env.dev up -d postgres redis

# Ждем готовности сервисов
echo "⏳ Ожидание готовности PostgreSQL и Redis..."
sleep 10

# Запускаем бота
echo "🤖 Запуск бота..."
docker-compose -f docker-compose.dev.yml --env-file .env.dev up bot

echo "✅ Бот запущен в режиме разработки!"
echo "📊 Для просмотра логов: docker-compose -f docker-compose.dev.yml logs -f"
echo "🛑 Для остановки: docker-compose -f docker-compose.dev.yml down"
