@echo off
echo 🚀 Запуск телеграм бота в режиме разработки...

REM Проверяем наличие .env.dev файла
if not exist ".env.dev" (
    echo ❌ Файл .env.dev не найден!
    echo 📝 Создайте файл .env.dev на основе .env.example
    echo copy .env.example .env.dev
    echo notepad .env.dev
    pause
    exit /b 1
)

REM Проверяем наличие BOT_TOKEN
findstr /C:"BOT_TOKEN=your_bot_token_here" .env.dev >nul
if %errorlevel% equ 0 (
    echo ❌ BOT_TOKEN не настроен в .env.dev!
    echo 📝 Отредактируйте файл .env.dev и добавьте реальный токен бота
    pause
    exit /b 1
)

echo ✅ Конфигурация найдена

REM Останавливаем существующие контейнеры
echo 🛑 Остановка существующих контейнеров...
docker-compose -f docker-compose.dev.yml --env-file .env.dev down

REM Собираем образ
echo 🔨 Сборка образа...
docker-compose -f docker-compose.dev.yml --env-file .env.dev build

REM Запускаем сервисы
echo 🚀 Запуск сервисов...
docker-compose -f docker-compose.dev.yml --env-file .env.dev up -d postgres redis

REM Ждем готовности сервисов
echo ⏳ Ожидание готовности PostgreSQL и Redis...
timeout /t 10 /nobreak >nul

REM Запускаем бота
echo 🤖 Запуск бота...
docker-compose -f docker-compose.dev.yml --env-file .env.dev up bot

echo ✅ Бот запущен в режиме разработки!
echo 📊 Для просмотра логов: docker-compose -f docker-compose.dev.yml logs -f
echo 🛑 Для остановки: docker-compose -f docker-compose.dev.yml down
pause
